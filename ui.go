package main

import (
	"fmt"
	"strconv"

	g "github.com/AllenDang/giu"
)

// editAction opens the action editor for a specific action
func editAction(index int) {
	state.editingAction = index
	action := state.config.Actions[index]
	state.tempKey = action.Key
	state.tempDuration = fmt.Sprintf("%.1f", action.Duration)
}

// saveActionChanges saves the changes to the edited action
func saveActionChanges() {
	if state.editingAction < 0 || state.editingAction >= len(state.config.Actions) {
		return
	}

	// Update the key if changed
	if state.tempKey != "" {
		// Check if it's a valid key
		if scanCode, ok := keyNameToScanCode[state.tempKey]; ok {
			state.config.Actions[state.editingAction].Key = state.tempKey
			state.config.Actions[state.editingAction].ScanCode = scanCode
		}
	}

	// Update the duration if changed
	if state.tempDuration != "" {
		duration, err := strconv.ParseFloat(state.tempDuration, 64)
		if err == nil && duration >= 0.1 && duration <= 10.0 {
			state.config.Actions[state.editingAction].Duration = duration
		}
	}

	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
}

// cancelActionEdit cancels the action edit
func cancelActionEdit() {
	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
}

// toggleActionEnabled toggles the enabled state of an action
func toggleActionEnabled(index int) {
	if index < 0 || index >= len(state.config.Actions) {
		return
	}
	state.config.Actions[index].Enabled = !state.config.Actions[index].Enabled
}

// renderConfigTab renders the configuration tab
func renderConfigTab() g.Layout {
	// Get all available keys
	keyNames := getAllKeyNames()

	// Create index for combo selection
	var selectedKeyIndex int32 = 0

	// Find current key index
	for i, key := range keyNames {
		if key == state.config.LockKey {
			selectedKeyIndex = int32(i)
			break
		}
	}

	// Timer interval as a slider
	var timerInterval float32 = float32(state.config.TimerInterval)

	// Space key spam interval
	var spaceInterval int32 = int32(state.config.SpaceSpamInterval)
	var spaceEnabled bool = state.config.SpaceSpamEnabled

	return g.Layout{
		g.Label(translate("Lock Key")),
		g.Row(
			g.Combo("##lockkey", state.config.LockKey, keyNames, &selectedKeyIndex).OnChange(func() {
				if selectedKeyIndex >= 0 && int(selectedKeyIndex) < len(keyNames) {
					setLockKey(keyNames[selectedKeyIndex])
				}
			}),
			g.Button(translate("Press key for action")).OnClick(func() {
				startRecordingKey(&state.config.LockKey, &state.config.LockScanCode)
			}),
		),
		g.Spacing(),
		g.Label(fmt.Sprintf("%s: %.1f %s", translate("Timer Interval"), state.config.TimerInterval, translate("seconds"))),
		g.SliderFloat(&timerInterval, 0.1, 10.0).OnChange(func() {
			setTimerInterval(float64(timerInterval))
		}),
		g.Spacing(),
		g.Separator(),
		g.Label(translate("Space Key Spam")),
		g.Checkbox(translate("Enable Space Key Spam"), &spaceEnabled).OnChange(func() {
			toggleSpaceSpam(spaceEnabled)
		}),
		g.Row(
			g.Label(translate("Space Key Interval")),
			g.SliderInt(&spaceInterval, 50, 2000).OnChange(func() {
				setSpaceSpamInterval(int(spaceInterval))
			}),
			g.Label(fmt.Sprintf("%d %s", spaceInterval, translate("ms"))),
		),
		g.Spacing(),
		g.Separator(),
		g.Label(translate("Game Process")),
		g.Button(translate("Detect Game")).OnClick(detectGameProcess),
		g.Spacing(),
		g.Row(
			g.Button(translate("Save Configuration")).OnClick(saveConfigToFile),
			g.Button(translate("Load Configuration")).OnClick(loadConfigFromFile),
		),
		g.Spacing(),
		g.Separator(),
		g.Label(translate("Overlay Mode")),
		g.Checkbox(translate("Enable Overlay"), &state.overlayEnabled),
		// Transparency slider if overlay is enabled
		g.Condition(state.overlayEnabled, g.Layout{
			g.SliderFloat(&state.transparency, 0.2, 1.0),
		}, nil),
	}
}

// renderActionsTab renders the actions tab with the list of actions
func renderActionsTab() g.Layout {
	var layout g.Layout

	// Add action list
	layout = append(layout, g.Label(translate("Action Sequence")))
	layout = append(layout, g.Separator())

	for i, action := range state.config.Actions {
		// Skip if we're currently editing this action
		if i == state.editingAction {
			continue
		}

		// Using a separate variable to capture the correct index for the closure
		actionIndex := i

		layout = append(layout, g.Row(
			g.Checkbox("##enabled"+strconv.Itoa(i), &state.config.Actions[i].Enabled),
			g.Label(fmt.Sprintf("%d. %s: %s, %s: %.1f %s",
				i+1, translate("Key"), action.Key,
				translate("Duration"), action.Duration,
				translate("seconds"))),
			g.Button(fmt.Sprintf("%s##%d", translate("Edit"), i)).OnClick(func() {
				editAction(actionIndex)
			}),
		))
	}

	// Show action editor if editing
	if state.editingAction >= 0 && state.editingAction < len(state.config.Actions) {
		// Get all available keys
		keyNames := getAllKeyNames()

		// Create index for combo selection
		var selectedKeyIndex int32 = 0

		// Find current key index
		for i, key := range keyNames {
			if key == state.tempKey {
				selectedKeyIndex = int32(i)
				break
			}
		}

		layout = append(layout,
			g.Separator(),
			g.Label(fmt.Sprintf("%s %d", translate("Editing Action"), state.editingAction+1)),
			g.Label(translate("Key")),
			g.Row(
				g.Combo("##actionkey", state.tempKey, keyNames, &selectedKeyIndex).OnChange(func() {
					if selectedKeyIndex >= 0 && int(selectedKeyIndex) < len(keyNames) {
						state.tempKey = keyNames[selectedKeyIndex]
					}
				}),
				g.Button(translate("Press key for action")).OnClick(func() {
					startRecordingKey(&state.tempKey, nil)
				}),
			),
			g.Label(translate("Duration (seconds)")),
			g.InputText(&state.tempDuration),
			g.Row(
				g.Button(translate("Save")).OnClick(saveActionChanges),
				g.Button(translate("Cancel")).OnClick(cancelActionEdit),
			),
		)
	}

	return layout
}

// renderDriverTab renders the driver installation tab
func renderDriverTab() g.Layout {
	return g.Layout{
		g.Label(translate("Interception Driver Status")),
		g.Separator(),
		g.Label(state.driverStatus),
		g.Spacing(),
		g.Label(translate("Installation:")),
		g.Button(translate("Install Driver")).OnClick(installDriver),
		g.Spacing(),
		g.Label(state.installStatus),
		g.Spacing(),
		g.Label(translate("NOTE: Installation requires administrator privileges.")),
		g.Label(translate("A system reboot is required.")),
	}
}

// renderMainTab renders the main tab with control buttons
func renderMainTab() g.Layout {
	buttonText := translate("Start Bot")
	buttonAction := startBot

	state.config.Mutex.Lock()
	running := state.config.Running
	state.config.Mutex.Unlock()

	if running {
		buttonText = translate("Stop Bot")
		buttonAction = stopBot
	}

	return g.Layout{
		g.Label(translate("Cabal Bot Control")),
		g.Separator(),
		g.Button(buttonText).Size(200, 40).OnClick(buttonAction),
		g.Spacing(),
		g.Label(state.statusMessage),
		g.Spacing(),
		g.Label(translate("Instructions:")),
		g.Label(translate("1. Install the Interception driver if not already installed.")),
		g.Label(translate("2. Configure your actions in the Actions tab.")),
		g.Label(translate("3. Configure the lock key in the Config tab.")),
		g.Label(translate("4. Click Start Bot to begin the sequence.")),
		g.Spacing(),
		g.Separator(),
		g.Label(translate("Game Process")),
		g.Button(translate("Detect Game")).OnClick(detectGameProcess),
		g.Condition(state.gameProcess.Found, g.Layout{
			g.Label(fmt.Sprintf("%s: %s", translate("Detected game path"), state.gameProcess.Path)),
		}, g.Layout{
			g.Label(translate("Game not detected")),
		}),
	}
}
