package main

import (
	"fmt"
	"image"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"

	g "github.com/AllenDang/giu"
	_ "image/jpeg"
	_ "image/png"
)

const (
	backgroundImageURL  = "https://i.ibb.co/67P9MrGT/auto-bgr.png"
	backgroundImagePath = "assets/background.png"
)

// downloadBackgroundImage downloads the background image from URL
func downloadBackgroundImage() error {
	// Create assets directory if it doesn't exist
	assetsDir := filepath.Dir(backgroundImagePath)
	if err := os.MkdirAll(assetsDir, 0755); err != nil {
		return fmt.Errorf("failed to create assets directory: %v", err)
	}

	// Check if file already exists
	if _, err := os.Stat(backgroundImagePath); err == nil {
		log.Println("Background image already exists, skipping download")
		return nil
	}

	log.Println("Downloading background image...")

	// Download the image
	resp, err := http.Get(backgroundImageURL)
	if err != nil {
		return fmt.Errorf("failed to download image: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download image: HTTP %d", resp.StatusCode)
	}

	// Create the file
	file, err := os.Create(backgroundImagePath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer file.Close()

	// Copy the image data
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to save image: %v", err)
	}

	log.Println("Background image downloaded successfully")
	return nil
}

// loadBackgroundTexture loads the background image as a texture
func loadBackgroundTexture() {
	// First, try to download the image if it doesn't exist
	if err := downloadBackgroundImage(); err != nil {
		log.Printf("Failed to download background image: %v", err)
		state.backgroundLoading = false
		return
	}

	// Load the image file
	file, err := os.Open(backgroundImagePath)
	if err != nil {
		log.Printf("Failed to open background image: %v", err)
		state.backgroundLoading = false
		return
	}
	defer file.Close()

	// Decode the image
	img, _, err := image.Decode(file)
	if err != nil {
		log.Printf("Failed to decode background image: %v", err)
		state.backgroundLoading = false
		return
	}

	// Create texture from image
	g.NewTextureFromRgba(img, func(tex *g.Texture) {
		state.backgroundTexture = tex
		state.backgroundLoaded = true
		state.backgroundLoading = false
		log.Println("Background texture loaded successfully")
	})
}

// renderBackgroundImage renders the background image if loaded
func renderBackgroundImage() g.Widget {
	if !state.backgroundLoaded || state.backgroundTexture == nil {
		return g.Dummy(0, 0)
	}

	// Get available region to scale background
	width, height := g.GetAvailableRegion()

	// Return the image widget that fills the available space
	return g.Image(state.backgroundTexture).Size(width, height)
}

// drawBackgroundImage draws the background image using canvas
func drawBackgroundImage() {
	if !state.backgroundLoaded || state.backgroundTexture == nil {
		return
	}

	// Get available region to draw background
	width, height := g.GetAvailableRegion()
	cursorPos := g.GetCursorScreenPos()

	// Draw background image on canvas
	canvas := g.GetCanvas()
	canvas.AddImage(state.backgroundTexture,
		cursorPos,
		image.Pt(int(float32(cursorPos.X)+width), int(float32(cursorPos.Y)+height)))
}

// withBackground wraps a layout with background image
func withBackground(layout ...g.Widget) g.Layout {
	// Add background drawing as first element
	result := g.Layout{
		g.Custom(drawBackgroundImage),
	}

	// Add all other widgets
	result = append(result, layout...)

	return result
}
