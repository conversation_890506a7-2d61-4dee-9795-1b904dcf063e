package main

import (
	"image/color"
	"log"
	"os"

	g "github.com/AllenDang/giu"
)

// Global state
var state AppState

// Initialize logger to file instead of console
func initLogger() {
	logFile, err := os.OpenFile("cabal_bot.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		// If we can't open the log file, we'll continue without logging
		return
	}
	state.logFile = logFile
	log.SetOutput(logFile)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	log.Println("Logger initialized")
}

// Initialization
func init() {
	// Initialize logger to file instead of console
	initLogger()

	// Create default config
	state.config = NewBotConfig()
	state.statusMessage = translate("Ready")
	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
	state.transparency = 1.0

	// Initialize wizard state
	state.wizardState = WizardState{
		CurrentStep: 1,
		TotalSteps:  4,
		Completed:   false,
	}

	// Initialize key recording state
	state.keyRecordingState = KeyRecordingState{
		Recording:      false,
		TargetKey:      nil,
		TargetScanCode: nil,
	}

	// Initialize game process info
	state.gameProcess = GameProcessInfo{
		ProcessID: 0,
		Path:      "",
		Found:     false,
	}

	// Check if driver is installed
	if IsInterceptionDriverInstalled() {
		state.driverStatus = translate("✓ Driver installed")
	} else {
		state.driverStatus = translate("⚠ Driver not installed")
	}

	// Initialize keyboard hook
	initKeyboardHook()
}

// updateOverlaySettings updates the window properties for overlay mode
func updateOverlaySettings(window *g.MasterWindow) {
	if state.overlayEnabled {
		// For overlay mode, we need to set a transparent background color
		// We can do this by setting the clear color alpha
		// Convert transparency (0.0-1.0) to an RGBA color with alpha
		alpha := uint8(state.transparency * 255)
		bgColor := color.RGBA{R: 0, G: 0, B: 0, A: alpha}
		window.SetBgColor(bgColor)

		// We can't directly set window flags after creation in current giu API
		// But we can make the window have Frameless and Floating flags at creation time
		// This would require modifying the window creation code in main()
	} else {
		// Reset to normal window with opaque background
		bgColor := color.RGBA{R: 0, G: 0, B: 0, A: 255} // Fully opaque
		window.SetBgColor(bgColor)
	}
}

// processKeyboardInput processes keyboard input captured by the global hook
func processKeyboardInput(key string, scanCode uint16) {
	if state.keyRecordingState.Recording {
		// Check if we need to update a target key
		if state.keyRecordingState.TargetKey != nil {
			*state.keyRecordingState.TargetKey = key

			// If we also need to update the scan code
			if state.keyRecordingState.TargetScanCode != nil {
				*state.keyRecordingState.TargetScanCode = scanCode
			}

			// Stop recording and update UI
			stopRecordingKey()
			g.Update()
		}
	}
}

// loop is the main GUI loop
func loop() {
	// Load background texture if not loaded yet
	if !state.backgroundLoaded && !state.backgroundLoading {
		state.backgroundLoading = true
		go loadBackgroundTexture()
	}

	// Display setup wizard if not completed
	if !state.wizardState.Completed {
		g.SingleWindow().Layout(
			withBackground(
				renderWizardStep(),
			)...,
		)
		return
	}

	// Create tabs for normal operation with background
	g.SingleWindow().Layout(
		withBackground(
			g.TabBar().TabItems(
				g.TabItem(translate("Main")).Layout(
					renderMainTab(),
				),
				g.TabItem(translate("Actions")).Layout(
					renderActionsTab(),
				),
				g.TabItem(translate("Config")).Layout(
					renderConfigTab(),
				),
				g.TabItem(translate("Driver")).Layout(
					renderDriverTab(),
				),
			),
		)...,
	)
}

func main() {
	// Create window with hidden console
	// When building, use: go build -ldflags="-H=windowsgui"

	// Initialize flags to 0 (resizable by default)
	var flags g.MasterWindowFlags = 0

	// If we want overlay capability, set only the overlay-related flags
	if state.overlayEnabled {
		flags |= g.MasterWindowFlagsFloating | g.MasterWindowFlagsFrameless
	}

	wnd := g.NewMasterWindow("Cabal Bot - v2.0", 600, 400, flags)

	// Set initial background color based on overlay settings
	if state.overlayEnabled {
		// Convert transparency (0.0-1.0) to uint8 alpha value (0-255)
		alpha := uint8(state.transparency * 255)
		wnd.SetBgColor(color.RGBA{R: 0, G: 0, B: 0, A: alpha})
	}

	// Run the main window loop
	wnd.Run(loop)

	// Cleanup when application closes
	cleanupKeyboardHook()

	if state.logFile != nil {
		state.logFile.Close()
	}
}
