@echo off
echo Installing required dependencies...
go get github.com/robotn/gohook

echo Building Cabal Bot with Vietnamese UI...
go build -ldflags="-H=windowsgui" -o CabalBotVN.exe main.go types.go config.go translation.go interception.go bot.go wizard.go ui.go keyboard_hook.go background.go
if %ERRORLEVEL% == 0 (
    echo Build successful! Created CabalBotVN.exe
) else (
    echo Build failed!
)
pause