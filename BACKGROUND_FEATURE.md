# Tính năng ảnh nền cho Cabal Bot

## Mô tả
Đã thêm tính năng ảnh nền cho phần mềm Cabal Bot. Ảnh nền sẽ được tự động tải từ URL và hiển thị phía sau giao diện người dùng.

## Tính năng mới
- **Tự động tải ảnh nền**: Ảnh sẽ được tải từ URL `https://i.ibb.co/67P9MrGT/auto-bgr.png` khi khởi động ứng dụng
- **Lưu trữ cục bộ**: Ảnh được lưu vào thư mục `assets/background.png` để tránh tải lại mỗi lần chạy
- **Hiển thị động**: Ảnh nền sẽ tự động điều chỉnh kích thước theo cửa sổ ứng dụng
- **Không ảnh hưởng hiệu suất**: Ảnh chỉ được tải một lần và hiển thị bằng GPU

## Các file đã thay đổi

### 1. `types.go`
- Thêm field `backgroundTexture *g.Texture` và `backgroundLoaded bool` vào struct `AppState`

### 2. `main.go`
- Thêm logic load background texture trong main loop (sau khi GUI context đã sẵn sàng)
- Cập nhật main loop để sử dụng `withBackground()` wrapper
- Thêm flag `backgroundLoading` để tránh load nhiều lần

### 3. `background.go` (file mới)
- `downloadBackgroundImage()`: Tải ảnh từ URL và lưu cục bộ
- `loadBackgroundTexture()`: Load ảnh thành texture cho giu
- `drawBackgroundImage()`: Vẽ ảnh nền bằng Canvas API
- `withBackground()`: Wrapper function để thêm ảnh nền vào layout

### 4. `build.bat`
- Thêm `background.go` vào danh sách file build

## Cách hoạt động
1. Trong main loop, kiểm tra nếu background chưa được load và chưa đang loading
2. Gọi `loadBackgroundTexture()` trong goroutine riêng sau khi GUI context đã sẵn sàng
3. Function này sẽ tải ảnh từ URL (nếu chưa có) và tạo texture
4. Trong main loop, `withBackground()` sẽ thêm ảnh nền vào mọi layout
5. Ảnh nền được vẽ bằng Canvas API phía sau các widget khác

## Tùy chỉnh ảnh nền
Để thay đổi ảnh nền, sửa constant `backgroundImageURL` trong file `background.go`:

```go
const (
    backgroundImageURL  = "URL_ẢNH_MỚI_CỦA_BẠN"
    backgroundImagePath = "assets/background.png"
)
```

## Lưu ý
- Ảnh nền sẽ được tải tự động khi khởi động lần đầu
- Nếu không có kết nối internet, ứng dụng vẫn hoạt động bình thường (không có ảnh nền)
- Ảnh được lưu trong thư mục `assets/` sẽ được tạo tự động
- Hỗ trợ các định dạng ảnh: PNG, JPEG

## Build và chạy
```bash
# Build ứng dụng
build.bat

# Chạy ứng dụng
CabalBotVN.exe
```

Ảnh nền sẽ xuất hiện ở cả wizard setup và giao diện chính của ứng dụng.
